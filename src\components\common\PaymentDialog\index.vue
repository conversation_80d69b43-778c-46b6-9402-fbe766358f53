<script setup lang="ts">
import { computed, ref, watch, onUnmounted } from 'vue'
import {
  NButton,
  NModal,
  NImage,
  NRadio,
  NRadioGroup,
  NSpace,
  NCard,
  NDivider,
  NIcon,
  useMessage,
} from 'naive-ui'
import { CheckmarkCircle, Time, Card } from '@vicons/ionicons5'
import to from 'await-to-js'
import { payUrl, getSPayUrl, getOrderInfo } from '@/api/pay'
import { useBasicLayout } from '@/hooks/useBasicLayout'

interface Props {
  visible: boolean
  orderInfo: {
    money: string
    name: string
    description?: string
    id?: string | number
  }
}

interface Emit {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const { isMobile } = useBasicLayout()
const message = useMessage()

// 支付状态：1-选择支付方式，2-显示支付二维码，3-支付成功
const paymentStep = ref(1)
const paymentMethod = ref('wechat')
const loading = ref(false)
const qrCodeUrl = ref('')
const orderNo = ref('')

let intervalId: string | number | NodeJS.Timer | undefined
const POLLING_INTERVAL = 3000

const show = computed({
  get() {
    return props.visible
  },
  set(visible: boolean) {
    emit('update:visible', visible)
  },
})

// 重置状态
const resetState = () => {
  paymentStep.value = 1
  qrCodeUrl.value = ''
  orderNo.value = ''
  loading.value = false
  paymentMethod.value = 'wechat'
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = undefined
  }
}

// 监听弹窗关闭
watch(show, (newValue) => {
  if (!newValue) {
    resetState()
  }
})

// 确认支付
const handleConfirmPayment = async () => {
  if (!props.orderInfo.money || !props.orderInfo.name) {
    message.error('订单信息不完整')
    return
  }

  loading.value = true
  
  try {
    if (paymentMethod.value === 'wechat') {
      // 微信支付
      const [err, result] = await to(payUrl({
        money: props.orderInfo.money,
        name: props.orderInfo.name
      }))
      
      if (err) {
        message.error(err.message || '获取支付二维码失败')
        loading.value = false
        return
      }
      
      qrCodeUrl.value = result.data.url
      orderNo.value = result.data.orderNo
      paymentStep.value = 2
      
      // 开始轮询订单状态
      intervalId = setInterval(checkOrderStatus, POLLING_INTERVAL)
      
    } else if (paymentMethod.value === 'alipay') {
      // 支付宝支付
      const [err, result] = await to(getSPayUrl({
        money: props.orderInfo.money,
        name: props.orderInfo.name
      }))
      
      if (err) {
        message.error(err.message || '获取支付链接失败')
        loading.value = false
        return
      }
      
      // 打开支付页面
      window.open(result.data || result, '_blank', 'noopener,noreferrer')
      message.info('请在新打开的页面完成支付，完成后点击"我已支付"')
      
      // 显示支付宝支付状态页面
      paymentStep.value = 2
      intervalId = setInterval(checkOrderStatus, POLLING_INTERVAL)
    }
  } catch (error) {
    message.error('支付请求失败')
    console.error('Payment error:', error)
  } finally {
    loading.value = false
  }
}

// 检查订单状态
const checkOrderStatus = async () => {
  if (!orderNo.value) return
  
  const [err, result] = await to(getOrderInfo(orderNo.value))
  
  if (err) {
    console.error('查询订单状态失败:', err)
    return
  }
  
  if (result.data.paymentStatus === '2') {
    paymentStep.value = 3
    clearInterval(intervalId)
    setTimeout(() => {
      message.success('支付成功！感谢您的购买')
      resetState()
      show.value = false
      emit('success')
    }, 2000)
  }
}

// 手动查询支付状态
const handleManualCheck = async () => {
  if (!orderNo.value) {
    message.error('订单号不存在')
    return
  }
  
  loading.value = true
  await checkOrderStatus()
  loading.value = false
  
  if (paymentStep.value !== 3) {
    message.info('订单尚未支付，请完成支付后再试')
  }
}

// 取消支付
const handleCancel = () => {
  resetState()
  show.value = false
  emit('cancel')
}

// 返回上一步
const handleBack = () => {
  if (paymentStep.value === 2) {
    paymentStep.value = 1
    qrCodeUrl.value = ''
    if (intervalId) {
      clearInterval(intervalId)
      intervalId = undefined
    }
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>

<template>
  <NModal
    v-model:show="show"
    :auto-focus="false"
    preset="card"
    :closable="paymentStep !== 2"
    :mask-closable="paymentStep !== 2"
    :style="{
      width: isMobile ? '95%' : '420px',
      maxWidth: '95vw',
    }"
    @close="handleCancel"
  >
    <template #header>
      <div class="payment-header">
        <span v-if="paymentStep === 1">选择支付方式</span>
        <span v-else-if="paymentStep === 2">扫码支付</span>
        <span v-else>支付成功</span>
      </div>
    </template>

    <!-- 第一步：选择支付方式 -->
    <div v-if="paymentStep === 1" class="payment-step-1">
      <!-- 订单信息卡片 -->
      <NCard class="order-card" :bordered="false">
        <div class="order-header">
          <NIcon size="20" color="#52c41a">
            <Card />
          </NIcon>
          <span class="order-title">订单信息</span>
        </div>
        <div class="order-content">
          <div class="order-item">
            <span class="label">商品名称：</span>
            <span class="value">{{ orderInfo.name }}</span>
          </div>
          <div v-if="orderInfo.description" class="order-item">
            <span class="label">商品描述：</span>
            <span class="value">{{ orderInfo.description }}</span>
          </div>
          <div class="order-item price-item">
            <span class="label">支付金额：</span>
            <span class="price">¥{{ orderInfo.money }}</span>
          </div>
        </div>
      </NCard>

      <NDivider style="margin: 20px 0;" />

      <!-- 支付方式选择 -->
      <div class="payment-methods">
        <div class="method-title">
          <NIcon size="18" color="#1890ff">
            <CheckmarkCircle />
          </NIcon>
          <span>选择支付方式</span>
        </div>
        <NRadioGroup v-model:value="paymentMethod" class="method-group">
          <NSpace vertical size="large">
            <NRadio value="wechat" class="payment-radio">
              <div class="payment-option">
                <div class="payment-icon wechat">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.900 7.852.194-.242-2.751-3.086-4.570-8.595-4.570z"/>
                  </svg>
                </div>
                <div class="payment-info">
                  <div class="payment-name">微信支付</div>
                  <div class="payment-desc">使用微信扫码支付</div>
                </div>
                <div class="payment-badge">推荐</div>
              </div>
            </NRadio>
            <NRadio value="alipay" class="payment-radio">
              <div class="payment-option">
                <div class="payment-icon alipay">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.56 12.25c0-1.43-.87-2.7-2.19-3.24-.42-6.77-6.25-12.01-13.37-12.01-7.12 0-12.95 5.24-13.37 12.01-1.32.54-2.19 1.81-2.19 3.24 0 1.95 1.58 3.53 3.53 3.53.68 0 1.31-.19 1.85-.52 1.85 1.12 4.08 1.77 6.47 1.77s4.62-.65 6.47-1.77c.54.33 1.17.52 1.85.52 1.95 0 3.53-1.58 3.53-3.53z"/>
                  </svg>
                </div>
                <div class="payment-info">
                  <div class="payment-name">支付宝</div>
                  <div class="payment-desc">跳转支付宝完成支付</div>
                </div>
              </div>
            </NRadio>
          </NSpace>
        </NRadioGroup>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <NButton size="large" @click="handleCancel" class="cancel-btn">
          取消
        </NButton>
        <NButton 
          type="primary" 
          size="large"
          :loading="loading"
          @click="handleConfirmPayment"
          class="confirm-btn"
        >
          立即支付 ¥{{ orderInfo.money }}
        </NButton>
      </div>
    </div>

    <!-- 第二步：支付二维码 -->
    <div v-else-if="paymentStep === 2" class="payment-step-2">
      <div v-if="paymentMethod === 'wechat'" class="qrcode-section">
        <div class="qrcode-header">
          <NIcon size="24" color="#07c160">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.900 7.852.194-.242-2.751-3.086-4.570-8.595-4.570z"/>
            </svg>
          </NIcon>
          <div class="payment-title">
            <div class="title">微信支付</div>
            <div class="amount">¥{{ orderInfo.money }}</div>
          </div>
        </div>

        <div class="qrcode-container">
          <NImage
            :src="qrCodeUrl"
            width="200"
            height="200"
            class="qrcode-image"
            :preview-disabled="true"
          />
          <div class="qrcode-tips">
            <p class="tip-main">请使用微信扫一扫</p>
            <p class="tip-sub">扫描上方二维码完成支付</p>
          </div>
        </div>
      </div>

      <div v-else class="alipay-section">
        <div class="alipay-header">
          <NIcon size="24" color="#1677ff">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M22.56 12.25c0-1.43-.87-2.7-2.19-3.24-.42-6.77-6.25-12.01-13.37-12.01-7.12 0-12.95 5.24-13.37 12.01-1.32.54-2.19 1.81-2.19 3.24 0 1.95 1.58 3.53 3.53 3.53.68 0 1.31-.19 1.85-.52 1.85 1.12 4.08 1.77 6.47 1.77s4.62-.65 6.47-1.77c.54.33 1.17.52 1.85.52 1.95 0 3.53-1.58 3.53-3.53z"/>
            </svg>
          </NIcon>
          <div class="payment-title">
            <div class="title">支付宝支付</div>
            <div class="amount">¥{{ orderInfo.money }}</div>
          </div>
        </div>

        <div class="alipay-waiting">
          <div class="waiting-icon">
            <NIcon size="48" color="#1677ff">
              <Time />
            </NIcon>
          </div>
          <p class="waiting-text">请在新打开的支付宝页面完成支付</p>
          <p class="waiting-sub">支付完成后，请点击下方"我已支付"按钮</p>
        </div>
      </div>

      <!-- 支付状态提示 -->
      <div class="payment-status">
        <div class="status-item">
          <span class="status-dot active"></span>
          <span class="status-text">等待支付</span>
        </div>
        <div class="status-line"></div>
        <div class="status-item">
          <span class="status-dot"></span>
          <span class="status-text">支付完成</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <NButton size="large" @click="handleBack" class="back-btn">
          返回
        </NButton>
        <NButton
          type="primary"
          size="large"
          :loading="loading"
          @click="handleManualCheck"
          class="check-btn"
        >
          我已支付
        </NButton>
      </div>
    </div>

    <!-- 第三步：支付成功 -->
    <div v-else-if="paymentStep === 3" class="payment-step-3">
      <div class="success-content">
        <div class="success-icon">
          <NIcon size="64" color="#52c41a">
            <CheckmarkCircle />
          </NIcon>
        </div>
        <div class="success-title">支付成功！</div>
        <div class="success-desc">感谢您的购买，订单已完成</div>
        <div class="success-amount">支付金额：¥{{ orderInfo.money }}</div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.payment-header {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  text-align: center;
}

/* 第一步样式 */
.payment-step-1 {
  padding: 8px 0;
}

.order-card {
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border: 1px solid #e8f4fd;
  border-radius: 12px;
  margin-bottom: 8px;
}

.order-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-weight: 600;
  color: #262626;
}

.order-title {
  font-size: 16px;
}

.order-content {
  space-y: 12px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item .label {
  color: #8c8c8c;
  font-size: 14px;
}

.order-item .value {
  color: #262626;
  font-size: 14px;
  font-weight: 500;
}

.price-item .price {
  color: #ff4d4f;
  font-size: 20px;
  font-weight: 600;
}

.payment-methods {
  margin: 20px 0;
}

.method-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.method-group {
  width: 100%;
}

.payment-radio {
  width: 100%;
  margin: 0;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.payment-option:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.payment-radio:checked .payment-option {
  border-color: #1890ff;
  background: #e6f7ff;
}

.payment-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.payment-icon.wechat {
  background: #e8f5e8;
  color: #07c160;
}

.payment-icon.alipay {
  background: #e6f7ff;
  color: #1677ff;
}

.payment-info {
  flex: 1;
}

.payment-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.payment-desc {
  font-size: 12px;
  color: #8c8c8c;
}

.payment-badge {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  position: absolute;
  top: -6px;
  right: 12px;
}

/* 第二步样式 */
.payment-step-2 {
  text-align: center;
  padding: 20px 0;
}

.qrcode-header,
.alipay-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
}

.payment-title .title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.payment-title .amount {
  font-size: 16px;
  color: #ff4d4f;
  font-weight: 600;
}

.qrcode-container {
  margin-bottom: 24px;
}

.qrcode-image {
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  margin-bottom: 16px;
}

.qrcode-tips .tip-main {
  font-size: 16px;
  color: #262626;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.qrcode-tips .tip-sub {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
}

.alipay-waiting {
  margin-bottom: 24px;
}

.waiting-icon {
  margin-bottom: 16px;
}

.waiting-text {
  font-size: 16px;
  color: #262626;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.waiting-sub {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
}

.payment-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 24px 0;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #d9d9d9;
}

.status-dot.active {
  background: #1890ff;
}

.status-text {
  font-size: 12px;
  color: #8c8c8c;
}

.status-line {
  width: 40px;
  height: 1px;
  background: #d9d9d9;
}

/* 第三步样式 */
.payment-step-3 {
  text-align: center;
  padding: 40px 20px;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  margin-bottom: 16px;
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.success-desc {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 16px;
}

.success-amount {
  font-size: 18px;
  color: #ff4d4f;
  font-weight: 600;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.cancel-btn,
.back-btn {
  flex: 1;
  height: 44px;
  border-radius: 8px;
}

.confirm-btn,
.check-btn {
  flex: 2;
  height: 44px;
  border-radius: 8px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .payment-option {
    padding: 12px;
  }

  .payment-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .payment-name {
    font-size: 14px;
  }

  .qrcode-image {
    width: 180px !important;
    height: 180px !important;
  }
}
</style>
