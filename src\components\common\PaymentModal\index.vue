<script setup lang="ts">
import { computed, ref, watch, onUnmounted } from 'vue'
import {
  NButton,
  NModal,
  NImage,
  NRadio,
  NRadioGroup,
  NSpace,
  NCard,
  NDivider,
  useMessage,
} from 'naive-ui'
import to from 'await-to-js'
import { payUrl, getSPayUrl, getOrderInfo } from '@/api/pay'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import IconSvg from '@/components/common/IconSvg/index.vue'

interface Props {
  visible: boolean
  orderInfo: {
    money: string
    name: string
    description?: string
  }
}

interface Emit {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const { isMobile } = useBasicLayout()
const message = useMessage()

// 支付方式：wechat-微信支付，alipay-支付宝支付
const paymentMethod = ref('wechat')
const loading = ref(false)
const qrCodeUrl = ref('')
const orderNo = ref('')
const paymentStep = ref(1) // 1-选择支付方式，2-显示支付二维码

let intervalId: string | number | NodeJS.Timer | undefined
const POLLING_INTERVAL = 5000

const show = computed({
  get() {
    return props.visible
  },
  set(visible: boolean) {
    emit('update:visible', visible)
  },
})

// 重置状态
const resetState = () => {
  paymentStep.value = 1
  qrCodeUrl.value = ''
  orderNo.value = ''
  loading.value = false
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = undefined
  }
}

// 监听弹窗关闭
watch(show, (newValue) => {
  if (!newValue) {
    resetState()
  }
})

// 确认支付方式并生成支付二维码
const handleConfirmPayment = async () => {
  if (!props.orderInfo.money || !props.orderInfo.name) {
    message.error('订单信息不完整')
    return
  }

  loading.value = true
  
  try {
    if (paymentMethod.value === 'wechat') {
      // 微信支付
      const [err, result] = await to(payUrl({
        money: props.orderInfo.money,
        name: props.orderInfo.name
      }))
      
      if (err) {
        message.error(err.message || '获取支付二维码失败')
        loading.value = false
        return
      }
      
      qrCodeUrl.value = result.data.url
      orderNo.value = result.data.orderNo
      paymentStep.value = 2
      
      // 开始轮询订单状态
      intervalId = setInterval(checkOrderStatus, POLLING_INTERVAL)
      
    } else if (paymentMethod.value === 'alipay') {
      // 支付宝支付 - 跳转到支付页面
      const [err, result] = await to(getSPayUrl({
        money: props.orderInfo.money,
        name: props.orderInfo.name
      }))
      
      if (err) {
        message.error(err.message || '获取支付链接失败')
        loading.value = false
        return
      }
      
      // 打开支付页面
      window.open(result.data || result, '_blank', 'noopener,noreferrer')
      message.info('请在新打开的页面完成支付')
      
      // 关闭弹窗
      show.value = false
      emit('success')
    }
  } catch (error) {
    message.error('支付请求失败')
    console.error('Payment error:', error)
  } finally {
    loading.value = false
  }
}

// 检查订单状态
const checkOrderStatus = async () => {
  if (!orderNo.value) return
  
  const [err, result] = await to(getOrderInfo(orderNo.value))
  
  if (err) {
    message.error(err.message)
    resetState()
    show.value = false
    return
  }
  
  if (result.data.paymentStatus === '2') {
    message.success('支付成功！感谢您的购买')
    resetState()
    show.value = false
    emit('success')
  }
}

// 取消支付
const handleCancel = () => {
  resetState()
  show.value = false
  emit('cancel')
}

// 返回选择支付方式
const handleBack = () => {
  paymentStep.value = 1
  qrCodeUrl.value = ''
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = undefined
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>

<template>
  <NModal
    v-model:show="show"
    :auto-focus="false"
    preset="card"
    :title="paymentStep === 1 ? '选择支付方式' : '扫码支付'"
    :style="{
      width: isMobile ? '95%' : '480px',
      maxWidth: '95vw',
    }"
    :closable="!loading"
    :mask-closable="!loading"
    @close="handleCancel"
  >
    <!-- 第一步：选择支付方式 -->
    <div v-if="paymentStep === 1" class="payment-selection">
      <!-- 订单信息 -->
      <NCard :bordered="false" class="order-info">
        <div class="order-details">
          <h3 class="order-title">{{ orderInfo.name }}</h3>
          <p v-if="orderInfo.description" class="order-desc">{{ orderInfo.description }}</p>
          <div class="order-price">
            <span class="price-label">支付金额：</span>
            <span class="price-value">¥{{ orderInfo.money }}</span>
          </div>
        </div>
      </NCard>

      <NDivider />

      <!-- 支付方式选择 -->
      <div class="payment-methods">
        <h4 class="method-title">选择支付方式</h4>
        <NRadioGroup v-model:value="paymentMethod" class="method-group">
          <NSpace vertical>
            <NRadio value="wechat" class="payment-option">
              <div class="payment-item">
                <div class="payment-icon wechat-icon">
                  <IconSvg icon="wechat-pay" width="24" height="24" />
                </div>
                <div class="payment-info">
                  <div class="payment-name">微信支付</div>
                  <div class="payment-desc">使用微信扫码支付</div>
                </div>
              </div>
            </NRadio>
            <NRadio value="alipay" class="payment-option">
              <div class="payment-item">
                <div class="payment-icon alipay-icon">
                  <IconSvg icon="alipay" width="24" height="24" />
                </div>
                <div class="payment-info">
                  <div class="payment-name">支付宝</div>
                  <div class="payment-desc">跳转支付宝完成支付</div>
                </div>
              </div>
            </NRadio>
          </NSpace>
        </NRadioGroup>
      </div>

      <!-- 操作按钮 -->
      <div class="payment-actions">
        <NButton @click="handleCancel" class="cancel-btn">
          取消
        </NButton>
        <NButton 
          type="primary" 
          :loading="loading"
          @click="handleConfirmPayment"
          class="confirm-btn"
        >
          确认支付
        </NButton>
      </div>
    </div>

    <!-- 第二步：显示支付二维码（仅微信支付） -->
    <div v-else-if="paymentStep === 2" class="payment-qrcode">
      <div class="qrcode-container">
        <NImage 
          :src="qrCodeUrl" 
          width="220"
          height="220"
          class="qrcode-image"
          :preview-disabled="true"
        />
        <div class="qrcode-tips">
          <p class="tip-text">请使用微信扫码支付</p>
          <p class="amount-text">支付金额：¥{{ orderInfo.money }}</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="qrcode-actions">
        <NButton @click="handleBack" class="back-btn">
          返回
        </NButton>
        <NButton @click="handleCancel" class="cancel-btn">
          取消支付
        </NButton>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.payment-selection {
  padding: 8px 0;
}

.order-info {
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.order-details {
  text-align: center;
}

.order-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.order-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 12px 0;
}

.order-price {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.price-label {
  font-size: 14px;
  color: #666;
}

.price-value {
  font-size: 20px;
  font-weight: 600;
  color: #ff4d4f;
}

.payment-methods {
  margin: 20px 0;
}

.method-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 16px 0;
}

.method-group {
  width: 100%;
}

.payment-option {
  width: 100%;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.payment-option:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.payment-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wechat-icon {
  background: #e8f5e8;
}

.alipay-icon {
  background: #e6f7ff;
}

.payment-info {
  flex: 1;
}

.payment-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.payment-desc {
  font-size: 12px;
  color: #999;
}

.payment-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.cancel-btn {
  min-width: 80px;
}

.confirm-btn {
  min-width: 100px;
}

.payment-qrcode {
  text-align: center;
  padding: 20px 0;
}

.qrcode-container {
  margin-bottom: 24px;
}

.qrcode-image {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 16px;
}

.qrcode-tips {
  margin-top: 16px;
}

.tip-text {
  font-size: 16px;
  color: #333;
  margin: 0 0 8px 0;
}

.amount-text {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.qrcode-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.back-btn,
.cancel-btn {
  min-width: 80px;
}
</style>
