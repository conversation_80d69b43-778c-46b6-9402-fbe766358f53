# 支付弹窗组件使用说明

## 概述

新的支付弹窗组件 `PaymentModal` 提供了统一的支付界面，支持微信支付和支付宝支付两种方式。

## 功能特性

- ✅ 支持微信支付（扫码支付）
- ✅ 支持支付宝支付（跳转支付）
- ✅ 响应式设计，适配移动端
- ✅ 订单状态轮询
- ✅ 支付成功/取消回调
- ✅ 优雅的UI设计

## 组件位置

```
src/components/common/PaymentModal/index.vue
```

## 使用方法

### 1. 导入组件

```vue
<script setup lang="ts">
import PaymentModal from '@/components/common/PaymentModal/index.vue'
</script>
```

### 2. 定义数据

```vue
<script setup lang="ts">
import { ref } from 'vue'

const paymentModalVisible = ref(false)
const orderInfo = ref({
  money: '99.00',
  name: '高级套餐',
  description: '包含AI对话、图片生成、文档处理等功能'
})

const handlePaymentSuccess = () => {
  console.log('支付成功')
  // 处理支付成功逻辑
}

const handlePaymentCancel = () => {
  console.log('支付取消')
  // 处理支付取消逻辑
}
</script>
```

### 3. 在模板中使用

```vue
<template>
  <!-- 触发支付的按钮 -->
  <NButton type="primary" @click="paymentModalVisible = true">
    立即购买
  </NButton>

  <!-- 支付弹窗 -->
  <PaymentModal
    v-model:visible="paymentModalVisible"
    :order-info="orderInfo"
    @success="handlePaymentSuccess"
    @cancel="handlePaymentCancel"
  />
</template>
```

## Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| visible | boolean | 是 | false | 控制弹窗显示/隐藏 |
| orderInfo | object | 是 | - | 订单信息对象 |

### orderInfo 对象结构

```typescript
interface OrderInfo {
  money: string        // 支付金额，如 "99.00"
  name: string         // 商品名称，如 "高级套餐"
  description?: string // 商品描述（可选）
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | boolean | 弹窗显示状态变化 |
| success | - | 支付成功回调 |
| cancel | - | 支付取消回调 |

## 支付流程

### 微信支付流程

1. 用户选择微信支付
2. 点击"确认支付"按钮
3. 调用后端接口获取支付二维码
4. 显示二维码供用户扫码
5. 开始轮询订单状态
6. 支付成功后触发 `success` 事件

### 支付宝支付流程

1. 用户选择支付宝支付
2. 点击"确认支付"按钮
3. 调用后端接口获取支付链接
4. 在新窗口打开支付页面
5. 用户在支付宝页面完成支付
6. 触发 `success` 事件

## 样式定制

组件使用了 scoped 样式，如需定制样式，可以通过以下方式：

1. 使用深度选择器覆盖样式
2. 修改组件内部的 CSS 变量
3. 直接修改组件源码

## 测试页面

访问 `/test-payment` 路由可以查看支付弹窗的演示效果。

## 注意事项

1. 确保后端支付接口正常工作
2. 微信支付需要配置正确的支付参数
3. 支付宝支付需要处理跨域问题
4. 建议在生产环境中添加更多的错误处理逻辑

## 已集成的页面

- `src/components/common/PromptStore/index.vue` - 套餐购买页面

## API 接口

组件依赖以下 API 接口：

- `payUrl(params)` - 获取微信支付二维码
- `getSPayUrl(params)` - 获取支付宝支付链接  
- `getOrderInfo(orderNo)` - 查询订单状态

确保这些接口在 `src/api/pay.ts` 中正确实现。
